# GitHub 热门项目统计 API

一个基于 Node.js 的 GitHub 热门项目统计工具，支持多平台部署。

## 🚀 快速部署

### Netlify 部署（推荐）

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/m2kall/github-trending-stats-api)

> **注意**：如果遇到部署问题，请确保在 Netlify 中选择 `main` 分支进行部署。

1. 点击上方按钮或手动连接 GitHub 仓库
2. 配置环境变量（可选）：
   - `GITHUB_TOKEN`: GitHub Personal Access Token
3. 部署完成！

详细部署指南请参考：[NETLIFY_DEPLOYMENT.md](./NETLIFY_DEPLOYMENT.md)

### Railway 部署

在Railway项目中设置以下环境变量：

```env
# 推荐配置
GITHUB_TOKEN=your_github_personal_access_token
NODE_ENV=production
```

## 🔧 环境变量配置

### GitHub Token（推荐）

为避免 API 限制，建议配置 GitHub Personal Access Token：

1. 访问 [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
2. 创建新 token，选择 `public_repo` 权限
3. 在部署平台中设置 `GITHUB_TOKEN` 环境变量
## 📊 部署后的新功能

### 🎯 核心增强功能
- **实时搜索过滤** - 支持项目名称、描述、作者搜索
- **多维度筛选** - 按语言、时间、stars排序
- **自动数据更新** - 定时任务自动更新数据

### 🔄 自动化功能
- **定时数据采集** - 每小时自动获取最新数据
- **智能重试机制** - API失败自动重试
- **速率限制管理** - 避免GitHub API限制
- **错误监控告警** - 自动检测和恢复

### 📱 用户体验提升
- **响应式设计** - 完美适配移动设备
- **实时加载状态** - 优雅的加载动画
- **离线缓存** - 网络断开时显示缓存数据
- **快速搜索** - 毫秒级搜索响应

