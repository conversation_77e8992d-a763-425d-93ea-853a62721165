{"name": "github-trending-stats-enhanced", "version": "2.0.0", "description": "增强版 GitHub 热门项目统计工具", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "build": "echo 'Netlify build completed successfully'", "netlify-dev": "netlify dev", "migrate": "node -e \"console.log('Migration skipped - supports both Railway and Netlify')\"", "test": "echo 'No tests specified'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "helmet": "^7.1.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.0", "node-cron": "^3.0.3"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"netlify-cli": "^17.0.0"}}