# Netlify 部署指南

本项目已适配 Netlify 平台，可以通过以下步骤进行部署。

## 🚀 快速部署

### 方法一：通过 Netlify 网站部署

1. **连接 GitHub 仓库**
   - 访问 [Netlify](https://netlify.com)
   - 点击 "New site from Git"
   - 选择 GitHub 并授权
   - 选择此仓库

2. **配置构建设置**
   - Build command: `npm run build`
   - Publish directory: `public`
   - Functions directory: `netlify/functions`

3. **设置环境变量**（可选但推荐）
   - 在 Netlify 控制台中，进入 Site settings > Environment variables
   - 添加以下环境变量：
     ```
     GITHUB_TOKEN=your_github_personal_access_token
     NODE_ENV=production
     ```

4. **部署**
   - 点击 "Deploy site"
   - 等待构建完成

### 方法二：通过 Netlify CLI 部署

1. **安装 Netlify CLI**
   ```bash
   npm install -g netlify-cli
   ```

2. **登录 Netlify**
   ```bash
   netlify login
   ```

3. **初始化项目**
   ```bash
   netlify init
   ```

4. **本地开发**
   ```bash
   netlify dev
   ```

5. **部署到生产环境**
   ```bash
   netlify deploy --prod
   ```

## 📋 配置说明

### netlify.toml 配置文件

项目包含 `netlify.toml` 配置文件，包含以下设置：

- **构建配置**：指定构建命令和发布目录
- **函数配置**：设置 Netlify Functions 目录
- **重定向规则**：将 API 请求重定向到 Netlify Functions
- **CORS 头设置**：允许跨域请求

### API 端点

部署后，以下 API 端点将可用：

- `GET /api/stats` - 获取 GitHub 热门项目统计
- `GET /api/by-language` - 按编程语言获取统计
- `GET /api/history` - 获取历史趋势数据
- `GET /api/health` - 健康检查

## 🔧 环境变量配置

### GitHub Token（推荐）

为了避免 GitHub API 限制，建议配置 GitHub Personal Access Token：

1. 访问 [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
2. 点击 "Generate new token"
3. 选择 "public_repo" 权限
4. 复制生成的 token
5. 在 Netlify 中设置环境变量 `GITHUB_TOKEN`

### 在 Netlify 中设置环境变量

1. 进入 Netlify 控制台
2. 选择你的站点
3. 进入 Site settings > Environment variables
4. 点击 "Add variable"
5. 添加变量名和值

## 🛠️ 本地开发

1. **克隆仓库**
   ```bash
   git clone <your-repo-url>
   cd github-trending-stats-api
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，添加你的 GitHub token
   ```

4. **启动本地开发服务器**
   ```bash
   # 使用 Netlify Dev（推荐）
   npm run netlify-dev
   
   # 或使用原始 Express 服务器
   npm run dev
   ```

## 📊 功能特性

- ✅ GitHub 热门项目统计
- ✅ 按时间周期分类（周、月、季、年）
- ✅ 按编程语言分类
- ✅ 历史趋势数据
- ✅ 响应式前端界面
- ✅ API 速率限制保护
- ✅ 错误处理和备用数据
- ✅ CORS 支持
- ✅ 健康检查端点

## 🔍 故障排除

### 常见问题

1. **API 请求失败**
   - 检查 GitHub Token 是否正确配置
   - 确认网络连接正常
   - 查看 Netlify Functions 日志

2. **构建失败**
   - 检查 Node.js 版本（需要 >= 18.0.0）
   - 确认所有依赖已正确安装
   - 查看构建日志中的错误信息

3. **函数超时**
   - Netlify Functions 默认超时时间为 10 秒
   - 如需更长时间，考虑升级到 Pro 计划

### 查看日志

在 Netlify 控制台中：
1. 进入 Functions 标签页
2. 点击具体的函数
3. 查看 "Function log" 获取详细日志

## 📞 支持

如果遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看 Netlify Functions 日志
3. 在项目仓库中创建 Issue
