exports.handler = async (event, context) => {
    // 设置 CORS 头
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
    };

    // 处理 OPTIONS 请求
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // 只处理 GET 请求
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: '方法不允许' })
        };
    }

    try {
        // 模拟历史趋势数据
        const mockTrendHistory = [
            { github_id: 1, date: '2025-05-25', period: 'weekly', total_projects: 1250 },
            { github_id: 1, date: '2025-05-26', period: 'weekly', total_projects: 1280 },
            { github_id: 1, date: '2025-05-27', period: 'weekly', total_projects: 1320 },
            { github_id: 1, date: '2025-05-28', period: 'weekly', total_projects: 1350 },
            { github_id: 1, date: '2025-05-29', period: 'weekly', total_projects: 1380 },
            { github_id: 1, date: '2025-05-30', period: 'weekly', total_projects: 1400 },
            { github_id: 1, date: '2025-06-01', period: 'weekly', total_projects: 1450 },

            { github_id: 2, date: '2025-05-25', period: 'weekly', total_projects: 800 },
            { github_id: 2, date: '2025-05-26', period: 'weekly', total_projects: 820 },
            { github_id: 2, date: '2025-05-27', period: 'weekly', total_projects: 850 },
            { github_id: 2, date: '2025-05-28', period: 'weekly', total_projects: 880 },
            { github_id: 2, date: '2025-05-29', period: 'weekly', total_projects: 900 },
            { github_id: 2, date: '2025-05-30', period: 'weekly', total_projects: 930 },
            { github_id: 2, date: '2025-06-01', period: 'weekly', total_projects: 950 },

            { github_id: 3, date: '2025-05-20', period: 'weekly', total_projects: 3000 },
            { github_id: 3, date: '2025-05-21', period: 'weekly', total_projects: 3050 },
            { github_id: 3, date: '2025-05-22', period: 'weekly', total_projects: 3100 },
            { github_id: 3, date: '2025-05-23', period: 'weekly', total_projects: 3150 },
            { github_id: 3, date: '2025-05-24', period: 'weekly', total_projects: 3200 },
            { github_id: 3, date: '2025-05-25', period: 'weekly', total_projects: 3250 },
            { github_id: 3, date: '2025-05-31', period: 'weekly', total_projects: 3300 }
        ];
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(mockTrendHistory)
        };
    } catch (error) {
        console.error('趋势历史API错误:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: '服务器内部错误' })
        };
    }
};
