const GitHubService = require('./shared/github-service');
const memoryStore = require('./shared/memory-store');

const githubService = new GitHubService();

// 数据更新函数
async function updateTrendingData() {
    try {
        console.log('开始更新GitHub trending数据...');
        
        const [weekly, monthly, quarterly, yearly, languageStats] = await Promise.all([
            githubService.getTrendingByPeriod(7, 10),
            githubService.getTrendingByPeriod(30, 10),
            githubService.getTrendingByPeriod(90, 10),
            githubService.getTrendingByPeriod(365, 3),
            githubService.getTrendingByLanguage()
        ]);

        memoryStore.projects = { weekly, monthly, quarterly, yearly };
        memoryStore.languageStats = languageStats;
        memoryStore.lastUpdate = new Date().toISOString();
        
        console.log('数据更新完成:', {
            weekly: weekly.length,
            monthly: monthly.length,
            quarterly: quarterly.length,
            yearly: yearly.length
        });
        
    } catch (error) {
        console.error('更新数据失败:', error.message);
    }
}

exports.handler = async (event, context) => {
    // 设置 CORS 头
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json'
    };

    // 处理 OPTIONS 请求
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // 只处理 GET 请求
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: '方法不允许' })
        };
    }

    try {
        // 如果没有数据或数据过期（超过1小时），则更新数据
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const lastUpdate = memoryStore.lastUpdate ? new Date(memoryStore.lastUpdate) : null;
        
        if (!lastUpdate || lastUpdate < oneHourAgo) {
            await updateTrendingData();
        }
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                ...memoryStore.projects,
                lastUpdate: memoryStore.lastUpdate
            })
        };
    } catch (error) {
        console.error('API 错误:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: '服务器内部错误' })
        };
    }
};
