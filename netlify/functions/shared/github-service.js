// GitHub API 服务类
class GitHubService {
    constructor() {
        this.token = process.env.GITHUB_TOKEN;
        this.baseURL = 'https://api.github.com';
        this.axios = require('axios');
    }

    async makeRequest(url, params = {}) {
        const headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'GitHub-Trending-Stats/2.0'
        };

        if (this.token) {
            headers['Authorization'] = `token ${this.token}`;
        }

        try {
            const response = await axios.get(url, { headers, params });
            return response.data;
        } catch (error) {
            console.error('GitHub API 请求失败:', error.message);
            throw error;
        }
    }

    async searchRepositories(query, options = {}) {
        const params = {
            q: query,
            sort: options.sort || 'stars',
            order: options.order || 'desc',
            per_page: options.perPage || 30
        };

        try {
            const data = await this.makeRequest(`${this.baseURL}/search/repositories`, params);
            
            return data.items.map(repo => ({
                github_id: repo.id,
                name: repo.name,
                full_name: repo.full_name,
                url: repo.html_url,
                description: repo.description || '暂无描述',
                stars: repo.stargazers_count,
                forks: repo.forks_count,
                language: repo.language || 'Unknown',
                topics: repo.topics || [],
                author: repo.owner.login,
                author_url: repo.owner.html_url,
                created_at: repo.created_at,
                updated_at: repo.updated_at
            }));
        } catch (error) {
            console.error('搜索仓库失败:', error.message);
            return this.getFallbackData();
        }
    }

    async getTrendingByPeriod(days, limit = 10) {
        const date = new Date();
        date.setDate(date.getDate() - days);
        const dateStr = date.toISOString().split('T')[0];
        
        const query = `created:>${dateStr}`;
        return await this.searchRepositories(query, { perPage: limit });
    }

    async getTrendingByLanguage() {
        const languages = ['JavaScript', 'Python', 'Java', 'TypeScript', 'Go', 'Rust'];
        const results = {};

        for (const language of languages) {
            try {
                const date = new Date();
                date.setDate(date.getDate() - 7);
                const dateStr = date.toISOString().split('T')[0];
                const query = `created:>${dateStr} language:${language}`;
                
                results[language] = await this.searchRepositories(query, { perPage: 5 });
                
                // 添加延迟避免速率限制
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`获取 ${language} 数据失败:`, error.message);
                results[language] = [];
            }
        }

        return results;
    }

    // 备用数据，当API失败时使用
    getFallbackData() {
        return [
            {
                github_id: 1,
                name: "trending-project-1",
                full_name: "example/trending-project-1",
                url: "https://github.com/example/trending-project-1",
                description: "一个很棒的开源项目，展示最新的技术趋势",
                stars: 2500,
                forks: 450,
                language: "JavaScript",
                topics: ["javascript", "nodejs", "react"],
                author: "example-user",
                author_url: "https://github.com/example-user",
                created_at: "2025-05-25T10:00:00Z",
                updated_at: "2025-06-01T10:00:00Z"
            },
            {
                github_id: 2,
                name: "awesome-python-tool",
                full_name: "dev/awesome-python-tool",
                url: "https://github.com/dev/awesome-python-tool",
                description: "强大的Python开发工具，提升开发效率",
                stars: 1800,
                forks: 320,
                language: "Python",
                topics: ["python", "tools", "automation"],
                author: "dev-team",
                author_url: "https://github.com/dev-team",
                created_at: "2025-05-28T15:30:00Z",
                updated_at: "2025-06-01T09:15:00Z"
            },
            {
                github_id: 3,
                name: "go-microservice",
                full_name: "gopher/go-microservice",
                url: "https://github.com/gopher/go-microservice",
                description: "高性能Go微服务框架，支持云原生部署",
                stars: 3200,
                forks: 680,
                language: "Go",
                topics: ["go", "microservices", "docker"],
                author: "gopher-dev",
                author_url: "https://github.com/gopher-dev",
                created_at: "2025-05-20T08:45:00Z",
                updated_at: "2025-05-31T16:20:00Z"
            }
        ];
    }
}

module.exports = GitHubService;
