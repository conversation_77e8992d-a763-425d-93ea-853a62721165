#!/bin/bash

# GitHub Trending Stats API - 部署到 GitHub 脚本
# 解决 Netlify "git ref refs/heads/main does not exist" 错误

echo "🚀 GitHub Trending Stats API - 部署到 GitHub"
echo "============================================="

# 检查是否已经有远程仓库
if git remote get-url origin >/dev/null 2>&1; then
    echo "✅ 远程仓库已配置"
    git remote -v
else
    echo "❌ 还没有配置远程仓库"
    echo ""
    echo "请按照以下步骤操作："
    echo ""
    echo "1. 访问 https://github.com"
    echo "2. 点击 'New repository'"
    echo "3. 输入仓库名称（例如：github-trending-stats-api）"
    echo "4. 选择 'Public' 或 'Private'"
    echo "5. 不要初始化 README、.gitignore 或 license"
    echo "6. 点击 'Create repository'"
    echo "7. 复制仓库 URL"
    echo ""
    read -p "请输入你的 GitHub 仓库 URL: " repo_url
    
    if [ -z "$repo_url" ]; then
        echo "❌ 仓库 URL 不能为空"
        exit 1
    fi
    
    echo "🔗 添加远程仓库..."
    git remote add origin "$repo_url"
    echo "✅ 远程仓库已添加"
fi

# 检查当前分支
current_branch=$(git branch --show-current)
echo "📍 当前分支: $current_branch"

# 检查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo "⚠️  发现未提交的更改，正在提交..."
    git add .
    git commit -m "Update: prepare for Netlify deployment"
fi

# 推送到远程仓库
echo "📤 推送代码到 GitHub..."
if git push -u origin main; then
    echo "✅ 代码已成功推送到 GitHub!"
    echo ""
    echo "🎉 现在你可以在 Netlify 中部署了："
    echo "1. 访问 https://netlify.com"
    echo "2. 点击 'New site from Git'"
    echo "3. 选择 GitHub 并授权"
    echo "4. 选择你的仓库"
    echo "5. 配置构建设置："
    echo "   - Build command: npm run build"
    echo "   - Publish directory: public"
    echo "   - Functions directory: netlify/functions"
    echo "6. 点击 'Deploy site'"
    echo ""
    echo "📖 详细部署指南请查看 NETLIFY_DEPLOYMENT.md"
else
    echo "❌ 推送失败，请检查："
    echo "1. 仓库 URL 是否正确"
    echo "2. 是否有推送权限"
    echo "3. 网络连接是否正常"
    echo ""
    echo "你可以手动执行："
    echo "git push -u origin main"
fi
